//
//  MouseKeyboardEvent.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import CoreGraphics

// MARK: - Event Types
enum EventType: UInt8, Codable {
    case mouseMove = 0x01
    case mouseLeftDown = 0x02
    case mouseLeftUp = 0x03
    case mouseRightDown = 0x04
    case mouseRightUp = 0x05
    case mouseMiddleDown = 0x06
    case mouseMiddleUp = 0x07
    case mouseScroll = 0x08
    case keyDown = 0x10
    case keyUp = 0x11
    case modifierChanged = 0x12
    case hotkey = 0x20
}

// MARK: - Mouse Event Data
struct MouseEventData: Codable {
    let x: Double
    let y: Double
    let deltaX: Double?
    let deltaY: Double?
    let scrollDeltaX: Double?
    let scrollDeltaY: Double?
    let clickCount: Int?
    
    init(x: Double = 0, y: Double = 0, deltaX: Double? = nil, deltaY: Double? = nil, 
         scrollDeltaX: Double? = nil, scrollDeltaY: Double? = nil, clickCount: Int? = nil) {
        self.x = x
        self.y = y
        self.deltaX = deltaX
        self.deltaY = deltaY
        self.scrollDeltaX = scrollDeltaX
        self.scrollDeltaY = scrollDeltaY
        self.clickCount = clickCount
    }
}

// MARK: - Keyboard Event Data
struct KeyboardEventData: Codable {
    let keyCode: UInt16
    let characters: String?
    let modifierFlags: UInt64
    let isRepeat: Bool
    
    init(keyCode: UInt16, characters: String? = nil, modifierFlags: UInt64 = 0, isRepeat: Bool = false) {
        self.keyCode = keyCode
        self.characters = characters
        self.modifierFlags = modifierFlags
        self.isRepeat = isRepeat
    }
}

// MARK: - Main Event Structure
struct MouseKeyboardEvent: Codable {
    let id: UUID
    let timestamp: TimeInterval
    let type: EventType
    let mouseData: MouseEventData?
    let keyboardData: KeyboardEventData?
    
    init(type: EventType, mouseData: MouseEventData? = nil, keyboardData: KeyboardEventData? = nil) {
        self.id = UUID()
        self.timestamp = Date().timeIntervalSince1970
        self.type = type
        self.mouseData = mouseData
        self.keyboardData = keyboardData
    }
}

// MARK: - Communication Protocol
struct CommunicationPacket: Codable {
    let header: PacketHeader
    let payload: Data
    let checksum: UInt32
    
    init(event: MouseKeyboardEvent) throws {
        let encoder = JSONEncoder()
        let eventData = try encoder.encode(event)
        
        self.header = PacketHeader(
            version: 1,
            packetType: .event,
            payloadSize: UInt32(eventData.count)
        )
        self.payload = eventData
        self.checksum = CommunicationPacket.calculateChecksum(data: eventData)
    }
    
    static func calculateChecksum(data: Data) -> UInt32 {
        return data.reduce(0) { result, byte in
            result &+ UInt32(byte)
        }
    }
}

struct PacketHeader: Codable {
    let version: UInt8
    let packetType: PacketType
    let payloadSize: UInt32
    let timestamp: TimeInterval
    
    init(version: UInt8, packetType: PacketType, payloadSize: UInt32) {
        self.version = version
        self.packetType = packetType
        self.payloadSize = payloadSize
        self.timestamp = Date().timeIntervalSince1970
    }
}

enum PacketType: UInt8, Codable {
    case handshake = 0x01
    case event = 0x02
    case ack = 0x03
    case heartbeat = 0x04
    case disconnect = 0x05
}

// MARK: - Connection State
enum ConnectionState: Equatable {
    case disconnected
    case connecting
    case connected
    case error(String)

    static func == (lhs: ConnectionState, rhs: ConnectionState) -> Bool {
        switch (lhs, rhs) {
        case (.disconnected, .disconnected),
             (.connecting, .connecting),
             (.connected, .connected):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}

// MARK: - Device Info
struct DeviceInfo: Codable {
    let deviceName: String
    let osVersion: String
    let appVersion: String
    let capabilities: [String]
    
    init() {
        self.deviceName = Host.current().localizedName ?? "Mac"
        self.osVersion = ProcessInfo.processInfo.operatingSystemVersionString
        self.appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        self.capabilities = ["mouse", "keyboard", "scroll", "hotkeys"]
    }
}
