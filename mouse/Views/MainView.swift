//
//  MainView.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI
import AppKit

struct MainView: View {
    @ObservedObject var usbManager: USBManager
    @ObservedObject var eventCaptureManager: EventCaptureManager
    @ObservedObject var communicationManager: CommunicationManager
    
    @State private var showingPermissionAlert = false
    
    var body: some View {
        VStack(spacing: 20) {
            // 连接状态卡片
            ConnectionStatusCard(
                connectionState: usbManager.connectionState,
                isCapturing: eventCaptureManager.isCapturing,
                selectedDevice: usbManager.selectedDevice
            )
            
            // 控制按钮
            ControlButtonsView(
                usbManager: usbManager,
                eventCaptureManager: eventCaptureManager,
                communicationManager: communicationManager,
                showingPermissionAlert: $showingPermissionAlert
            )
            
            // 实时信息
            RealTimeInfoView(
                eventCaptureManager: eventCaptureManager,
                communicationManager: communicationManager,
                usbManager: usbManager
            )
            
            Spacer()
        }
        .padding()
        .alert("需要辅助功能权限", isPresented: $showingPermissionAlert) {
            Button("打开系统偏好设置") {
                openAccessibilitySettings()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("此应用需要辅助功能权限来捕获键盘和鼠标事件。请在系统偏好设置 > 安全性与隐私 > 辅助功能中启用此应用。")
        }
    }
    
    private func openAccessibilitySettings() {
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
        NSWorkspace.shared.open(url)
    }
}

struct ConnectionStatusCard: View {
    let connectionState: ConnectionState
    let isCapturing: Bool
    let selectedDevice: USBDevice?
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: statusIcon)
                    .foregroundColor(statusColor)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("连接状态")
                        .font(.headline)
                    Text(statusText)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Circle()
                    .fill(statusColor)
                    .frame(width: 12, height: 12)
            }
            
            if let device = selectedDevice {
                Divider()
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("设备信息")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(device.name)
                            .font(.subheadline)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("事件捕获")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Text(isCapturing ? "已启用" : "已禁用")
                            .font(.subheadline)
                            .foregroundColor(isCapturing ? .green : .orange)
                    }
                }
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
    
    private var statusIcon: String {
        switch connectionState {
        case .connected:
            return "checkmark.circle.fill"
        case .connecting:
            return "arrow.triangle.2.circlepath"
        case .disconnected:
            return "xmark.circle.fill"
        case .error:
            return "exclamationmark.triangle.fill"
        }
    }
    
    private var statusColor: Color {
        switch connectionState {
        case .connected:
            return .green
        case .connecting:
            return .blue
        case .disconnected:
            return .gray
        case .error:
            return .red
        }
    }
    
    private var statusText: String {
        switch connectionState {
        case .connected:
            return "已连接到Windows设备"
        case .connecting:
            return "正在连接..."
        case .disconnected:
            return "未连接"
        case .error(let message):
            return "连接错误: \(message)"
        }
    }
}

struct ControlButtonsView: View {
    @ObservedObject var usbManager: USBManager
    @ObservedObject var eventCaptureManager: EventCaptureManager
    @ObservedObject var communicationManager: CommunicationManager
    @Binding var showingPermissionAlert: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            // 连接/断开按钮
            Button(action: toggleConnection) {
                HStack {
                    Image(systemName: connectionButtonIcon)
                    Text(connectionButtonText)
                }
                .frame(maxWidth: .infinity)
            }
            .buttonStyle(.borderedProminent)
            .disabled(usbManager.selectedDevice == nil && usbManager.connectionState == .disconnected)
            
            // 事件捕获按钮
            Button(action: toggleEventCapture) {
                HStack {
                    Image(systemName: eventCaptureManager.isCapturing ? "stop.circle" : "play.circle")
                    Text(eventCaptureManager.isCapturing ? "停止捕获" : "开始捕获")
                }
                .frame(maxWidth: .infinity)
            }
            .buttonStyle(.bordered)
        }
    }
    
    private var connectionButtonIcon: String {
        switch usbManager.connectionState {
        case .connected:
            return "stop.circle"
        case .connecting:
            return "arrow.triangle.2.circlepath"
        default:
            return "play.circle"
        }
    }
    
    private var connectionButtonText: String {
        switch usbManager.connectionState {
        case .connected:
            return "断开连接"
        case .connecting:
            return "连接中..."
        default:
            return "连接设备"
        }
    }
    
    private func toggleConnection() {
        switch usbManager.connectionState {
        case .connected:
            communicationManager.disconnect()
        case .disconnected:
            communicationManager.connect()
        default:
            break
        }
    }
    
    private func toggleEventCapture() {
        if eventCaptureManager.isCapturing {
            eventCaptureManager.stopCapturing()
        } else {
            if eventCaptureManager.checkPermissions() {
                eventCaptureManager.startCapturing()
            } else {
                showingPermissionAlert = true
            }
        }
    }
}

struct RealTimeInfoView: View {
    @ObservedObject var eventCaptureManager: EventCaptureManager
    @ObservedObject var communicationManager: CommunicationManager
    @ObservedObject var usbManager: USBManager
    
    var body: some View {
        VStack(spacing: 16) {
            Text("实时信息")
                .font(.headline)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                InfoCard(
                    title: "捕获事件",
                    value: "\(eventCaptureManager.capturedEventsCount)",
                    icon: "cursorarrow.click.2"
                )
                
                InfoCard(
                    title: "发送数据",
                    value: formatBytes(communicationManager.bytesSent),
                    icon: "arrow.up.circle"
                )
                
                InfoCard(
                    title: "接收数据",
                    value: formatBytes(communicationManager.bytesReceived),
                    icon: "arrow.down.circle"
                )
                
                InfoCard(
                    title: "延迟",
                    value: String(format: "%.1f ms", communicationManager.latency * 1000),
                    icon: "speedometer"
                )
                
                InfoCard(
                    title: "连接状态",
                    value: communicationManager.isConnected ? "在线" : "离线",
                    icon: communicationManager.isConnected ? "wifi" : "wifi.slash"
                )
                
                InfoCard(
                    title: "设备数量",
                    value: "\(usbManager.connectedDevices.count)",
                    icon: "externaldrive"
                )
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: Int64(bytes))
    }
}

struct InfoCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.accentColor)
            
            Text(value)
                .font(.headline)
                .lineLimit(1)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(1)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(NSColor.tertiarySystemFill))
        .cornerRadius(8)
    }
}

#Preview {
    let usbMgr = USBManager()
    let eventMgr = EventCaptureManager()
    let commMgr = CommunicationManager(usbManager: usbMgr, eventCaptureManager: eventMgr)

    return MainView(
        usbManager: usbMgr,
        eventCaptureManager: eventMgr,
        communicationManager: commMgr
    )
}
