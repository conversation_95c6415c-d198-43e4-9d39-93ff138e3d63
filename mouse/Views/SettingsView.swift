//
//  SettingsView.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI
import CoreGraphics
import AppKit

struct SettingsView: View {
    @ObservedObject var eventCaptureManager: EventCaptureManager
    @AppStorage("autoStartCapture") private var autoStartCapture = false
    @AppStorage("showNotifications") private var showNotifications = true
    @AppStorage("minimizeToTray") private var minimizeToTray = false
    @AppStorage("hotkeyEnabled") private var hotkeyEnabled = true
    
    @State private var selectedModifiers: CGEventFlags = [.maskCommand, .maskShift]
    @State private var selectedKeyCode: CGKeyCode = 0x31 // Space
    @State private var showingHotkeyRecorder = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("设置")
                .font(.title2)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            ScrollView {
                VStack(spacing: 24) {
                    // 常规设置
                    SettingsSection(title: "常规") {
                        SettingsToggle(
                            title: "自动开始捕获",
                            subtitle: "应用启动时自动开始捕获键鼠事件",
                            isOn: $autoStartCapture
                        )
                        
                        SettingsToggle(
                            title: "显示通知",
                            subtitle: "连接状态变化时显示系统通知",
                            isOn: $showNotifications
                        )
                        
                        SettingsToggle(
                            title: "最小化到系统托盘",
                            subtitle: "关闭窗口时最小化到系统托盘而不是退出",
                            isOn: $minimizeToTray
                        )
                    }
                    
                    // 热键设置
                    SettingsSection(title: "热键") {
                        SettingsToggle(
                            title: "启用热键",
                            subtitle: "使用热键快速切换键鼠控制",
                            isOn: $hotkeyEnabled
                        )
                        
                        if hotkeyEnabled {
                            HotkeyConfigView(
                                modifiers: $selectedModifiers,
                                keyCode: $selectedKeyCode,
                                showingRecorder: $showingHotkeyRecorder
                            )
                        }
                    }
                    
                    // 权限设置
                    SettingsSection(title: "权限") {
                        PermissionsView(eventCaptureManager: eventCaptureManager)
                    }
                    
                    // 高级设置
                    SettingsSection(title: "高级") {
                        AdvancedSettingsView()
                    }
                    
                    // 关于
                    SettingsSection(title: "关于") {
                        AboutView()
                    }
                }
            }
        }
        .padding()
        .sheet(isPresented: $showingHotkeyRecorder) {
            HotkeyRecorderView(
                modifiers: $selectedModifiers,
                keyCode: $selectedKeyCode
            )
        }
        .onChange(of: hotkeyEnabled) { enabled in
            eventCaptureManager.enableHotkey(enabled)
        }
        .onChange(of: selectedModifiers) { modifiers in
            eventCaptureManager.setHotkey(modifiers: modifiers, keyCode: selectedKeyCode)
        }
        .onChange(of: selectedKeyCode) { keyCode in
            eventCaptureManager.setHotkey(modifiers: selectedModifiers, keyCode: keyCode)
        }
    }
}

struct SettingsSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .foregroundColor(.primary)
            
            VStack(spacing: 8) {
                content
            }
            .padding()
            .background(Color(.controlBackgroundColor))
            .cornerRadius(12)
        }
    }
}

struct SettingsToggle: View {
    let title: String
    let subtitle: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
    }
}

struct HotkeyConfigView: View {
    @Binding var modifiers: CGEventFlags
    @Binding var keyCode: CGKeyCode
    @Binding var showingRecorder: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("热键组合")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("当前: \(hotkeyDescription)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button("设置") {
                showingRecorder = true
            }
            .buttonStyle(.bordered)
        }
    }
    
    private var hotkeyDescription: String {
        var parts: [String] = []
        
        if modifiers.contains(.maskCommand) {
            parts.append("⌘")
        }
        if modifiers.contains(.maskShift) {
            parts.append("⇧")
        }
        if modifiers.contains(.maskAlternate) {
            parts.append("⌥")
        }
        if modifiers.contains(.maskControl) {
            parts.append("⌃")
        }
        
        parts.append(keyCodeToString(keyCode))
        
        return parts.joined(separator: " + ")
    }
    
    private func keyCodeToString(_ keyCode: CGKeyCode) -> String {
        switch keyCode {
        case 0x31: return "Space"
        case 0x35: return "Esc"
        case 0x24: return "Return"
        case 0x30: return "Tab"
        default: return "Key \(keyCode)"
        }
    }
}

struct HotkeyRecorderView: View {
    @Binding var modifiers: CGEventFlags
    @Binding var keyCode: CGKeyCode
    @Environment(\.dismiss) private var dismiss
    
    @State private var isRecording = false
    @State private var recordedModifiers: CGEventFlags = []
    @State private var recordedKeyCode: CGKeyCode = 0
    
    var body: some View {
        VStack(spacing: 20) {
            Text("设置热键")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("请按下您想要设置的热键组合")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                if isRecording {
                    Text("正在录制...")
                        .font(.headline)
                        .foregroundColor(.blue)
                } else {
                    Text("点击开始录制")
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
                
                if recordedKeyCode != 0 {
                    Text("录制到: \(hotkeyDescription)")
                        .font(.subheadline)
                        .padding()
                        .background(Color(.controlBackgroundColor))
                        .cornerRadius(8)
                }
            }
            .frame(height: 100)
            
            HStack(spacing: 16) {
                Button("取消") {
                    dismiss()
                }
                .buttonStyle(.bordered)
                
                Button(isRecording ? "停止录制" : "开始录制") {
                    if isRecording {
                        stopRecording()
                    } else {
                        startRecording()
                    }
                }
                .buttonStyle(.borderedProminent)
                
                Button("保存") {
                    saveHotkey()
                }
                .buttonStyle(.borderedProminent)
                .disabled(recordedKeyCode == 0)
            }
        }
        .padding()
        .frame(width: 400, height: 300)
    }
    
    private var hotkeyDescription: String {
        var parts: [String] = []
        
        if recordedModifiers.contains(.maskCommand) {
            parts.append("⌘")
        }
        if recordedModifiers.contains(.maskShift) {
            parts.append("⇧")
        }
        if recordedModifiers.contains(.maskAlternate) {
            parts.append("⌥")
        }
        if recordedModifiers.contains(.maskControl) {
            parts.append("⌃")
        }
        
        parts.append(keyCodeToString(recordedKeyCode))
        
        return parts.joined(separator: " + ")
    }
    
    private func keyCodeToString(_ keyCode: CGKeyCode) -> String {
        switch keyCode {
        case 0x31: return "Space"
        case 0x35: return "Esc"
        case 0x24: return "Return"
        case 0x30: return "Tab"
        default: return "Key \(keyCode)"
        }
    }
    
    private func startRecording() {
        isRecording = true
        recordedModifiers = []
        recordedKeyCode = 0
        
        // 这里应该实现键盘事件监听
        // 由于预览限制，暂时模拟
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            recordedModifiers = [.maskCommand, .maskShift]
            recordedKeyCode = 0x31
            stopRecording()
        }
    }
    
    private func stopRecording() {
        isRecording = false
    }
    
    private func saveHotkey() {
        modifiers = recordedModifiers
        keyCode = recordedKeyCode
        dismiss()
    }
}

struct PermissionsView: View {
    @ObservedObject var eventCaptureManager: EventCaptureManager
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("辅助功能权限")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    Text("需要此权限来捕获键盘和鼠标事件")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                HStack {
                    Image(systemName: eventCaptureManager.checkPermissions() ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .foregroundColor(eventCaptureManager.checkPermissions() ? .green : .red)
                    
                    Text(eventCaptureManager.checkPermissions() ? "已授权" : "未授权")
                        .font(.caption)
                        .foregroundColor(eventCaptureManager.checkPermissions() ? .green : .red)
                }
            }
            
            if !eventCaptureManager.checkPermissions() {
                Button("打开系统偏好设置") {
                    openAccessibilitySettings()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity, alignment: .trailing)
            }
        }
    }
    
    private func openAccessibilitySettings() {
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
        NSWorkspace.shared.open(url)
    }
}

struct AdvancedSettingsView: View {
    @AppStorage("bufferSize") private var bufferSize = 1024
    @AppStorage("reconnectAttempts") private var reconnectAttempts = 5
    @AppStorage("heartbeatInterval") private var heartbeatInterval = 5.0
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("缓冲区大小")
                    .font(.subheadline)
                Spacer()
                TextField("", value: $bufferSize, format: .number)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 80)
                Text("KB")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("重连尝试次数")
                    .font(.subheadline)
                Spacer()
                TextField("", value: $reconnectAttempts, format: .number)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 80)
            }
            
            HStack {
                Text("心跳间隔")
                    .font(.subheadline)
                Spacer()
                TextField("", value: $heartbeatInterval, format: .number)
                    .textFieldStyle(.roundedBorder)
                    .frame(width: 80)
                Text("秒")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct AboutView: View {
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("版本")
                    .font(.subheadline)
                Spacer()
                Text("1.0.0")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("开发者")
                    .font(.subheadline)
                Spacer()
                Text("lincoo")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            HStack {
                Text("构建日期")
                    .font(.subheadline)
                Spacer()
                Text("2025-07-14")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
}

#Preview {
    SettingsView(eventCaptureManager: EventCaptureManager())
}
