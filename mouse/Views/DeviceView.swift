//
//  DeviceView.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct DeviceView: View {
    @ObservedObject var usbManager: USBManager
    @State private var showingDeviceDetails = false
    @State private var selectedDeviceForDetails: USBDevice?
    
    var body: some View {
        VStack(spacing: 20) {
            // 设备列表标题
            HStack {
                Text("USB设备列表")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("刷新") {
                    // 触发设备扫描
                    usbManager.objectWillChange.send()
                }
                .buttonStyle(.bordered)
            }
            
            // 设备列表
            if usbManager.connectedDevices.isEmpty {
                EmptyDeviceListView()
            } else {
                DeviceListView(
                    devices: usbManager.connectedDevices,
                    selectedDevice: usbManager.selectedDevice,
                    onDeviceSelected: { device in
                        usbManager.selectedDevice = device
                    },
                    onDeviceDetails: { device in
                        selectedDeviceForDetails = device
                        showingDeviceDetails = true
                    }
                )
            }
            
            Spacer()
        }
        .padding()
        .sheet(isPresented: $showingDeviceDetails) {
            if let device = selectedDeviceForDetails {
                DeviceDetailsView(device: device)
            }
        }
    }
}

struct EmptyDeviceListView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "externaldrive.badge.questionmark")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("未找到USB设备")
                .font(.headline)
            
            Text("请确保Windows设备已通过USB连接，并且安装了对应的驱动程序。")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct DeviceListView: View {
    let devices: [USBDevice]
    let selectedDevice: USBDevice?
    let onDeviceSelected: (USBDevice) -> Void
    let onDeviceDetails: (USBDevice) -> Void
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(devices) { device in
                    DeviceRowView(
                        device: device,
                        isSelected: selectedDevice?.id == device.id,
                        onSelect: { onDeviceSelected(device) },
                        onDetails: { onDeviceDetails(device) }
                    )
                }
            }
            .padding(.vertical, 8)
        }
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct DeviceRowView: View {
    let device: USBDevice
    let isSelected: Bool
    let onSelect: () -> Void
    let onDetails: () -> Void
    
    var body: some View {
        HStack(spacing: 16) {
            // 设备图标
            VStack {
                Image(systemName: deviceIcon)
                    .font(.title2)
                    .foregroundColor(device.isMouseKeyboardDevice ? .green : .secondary)
                
                if device.isMouseKeyboardDevice {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)
                }
            }
            .frame(width: 50)
            
            // 设备信息
            VStack(alignment: .leading, spacing: 4) {
                Text(device.name)
                    .font(.headline)
                    .lineLimit(1)
                
                HStack {
                    Text("厂商ID: \(String(format: "0x%04X", device.vendorID))")
                    Text("•")
                    Text("产品ID: \(String(format: "0x%04X", device.productID))")
                }
                .font(.caption)
                .foregroundColor(.secondary)
                
                if device.isMouseKeyboardDevice {
                    Text("键鼠共享设备")
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.green.opacity(0.2))
                        .foregroundColor(.green)
                        .cornerRadius(4)
                }
            }
            
            Spacer()
            
            // 操作按钮
            HStack(spacing: 8) {
                Button("详情") {
                    onDetails()
                }
                .buttonStyle(BorderedButtonStyle())
                .controlSize(.small)
                
                Button(isSelected ? "已选择" : "选择") {
                    onSelect()
                }
                .buttonStyle(isSelected ? BorderedProminentButtonStyle() : BorderedButtonStyle())
                .controlSize(.small)
                .disabled(isSelected)
            }
        }
        .padding()
        .background(isSelected ? Color.accentColor.opacity(0.1) : Color(NSColor.tertiarySystemFill))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(isSelected ? Color.accentColor : Color.clear, lineWidth: 2)
        )
    }
    
    private var deviceIcon: String {
        if device.isMouseKeyboardDevice {
            return "computermouse.fill"
        } else {
            return "externaldrive"
        }
    }
}

struct DeviceDetailsView: View {
    let device: USBDevice
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 20) {
                // 设备基本信息
                VStack(alignment: .leading, spacing: 12) {
                    Text("基本信息")
                        .font(.headline)
                    
                    DetailRow(label: "设备名称", value: device.name)
                    DetailRow(label: "厂商ID", value: String(format: "0x%04X", device.vendorID))
                    DetailRow(label: "产品ID", value: String(format: "0x%04X", device.productID))
                    DetailRow(label: "设备类型", value: device.isMouseKeyboardDevice ? "键鼠共享设备" : "普通USB设备")
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)
                
                // 兼容性信息
                VStack(alignment: .leading, spacing: 12) {
                    Text("兼容性")
                        .font(.headline)
                    
                    HStack {
                        Image(systemName: device.isMouseKeyboardDevice ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(device.isMouseKeyboardDevice ? .green : .red)
                        
                        Text(device.isMouseKeyboardDevice ? "支持键鼠共享" : "不支持键鼠共享")
                            .font(.subheadline)
                    }
                    
                    if !device.isMouseKeyboardDevice {
                        Text("此设备不是键鼠共享设备。请确保Windows端安装了正确的驱动程序。")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 4)
                    }
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationTitle("设备详情")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .frame(width: 500, height: 400)
    }
}

struct DetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.subheadline)
                .textSelection(.enabled)
            
            Spacer()
        }
    }
}

#Preview {
    DeviceView(usbManager: USBManager())
}
