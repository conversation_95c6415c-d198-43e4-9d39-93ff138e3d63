//
//  StatisticsView.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct StatisticsView: View {
    @ObservedObject var communicationManager: CommunicationManager
    @State private var selectedTimeRange: TimeRange = .hour
    @State private var dataPoints: [DataPoint] = []
    @State private var timer: Timer?
    
    var body: some View {
        VStack(spacing: 20) {
            // 时间范围选择
            HStack {
                Text("数据统计")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("时间范围", selection: $selectedTimeRange) {
                    ForEach(TimeRange.allCases, id: \.self) { range in
                        Text(range.displayName).tag(range)
                    }
                }
                .pickerStyle(.segmented)
                .frame(width: 300)
            }
            
            // 统计卡片
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                StatCard(
                    title: "总发送",
                    value: formatBytes(communicationManager.bytesSent),
                    icon: "arrow.up.circle.fill",
                    color: .blue
                )
                
                StatCard(
                    title: "总接收",
                    value: formatBytes(communicationManager.bytesReceived),
                    icon: "arrow.down.circle.fill",
                    color: .green
                )
                
                StatCard(
                    title: "平均延迟",
                    value: String(format: "%.1f ms", communicationManager.latency * 1000),
                    icon: "speedometer",
                    color: .orange
                )
                
                StatCard(
                    title: "连接时长",
                    value: formatDuration(getConnectionDuration()),
                    icon: "clock.fill",
                    color: .purple
                )
            }
            
            // 数据传输图表
            VStack(alignment: .leading, spacing: 12) {
                Text("数据传输趋势")
                    .font(.headline)
                
                SimpleChartView(dataPoints: dataPoints, type: .dataTransfer)
                    .frame(height: 200)
                    .padding()
                    .background(Color(.controlBackgroundColor))
                    .cornerRadius(12)
            }
            
            // 延迟图表
            VStack(alignment: .leading, spacing: 12) {
                Text("延迟趋势")
                    .font(.headline)
                
                SimpleChartView(dataPoints: dataPoints, type: .latency)
                    .frame(height: 150)
                    .padding()
                    .background(Color(.controlBackgroundColor))
                    .cornerRadius(12)
            }
            
            Spacer()
        }
        .padding()
        .onAppear {
            startDataCollection()
        }
        .onDisappear {
            stopDataCollection()
        }
        .onChange(of: selectedTimeRange) { _ in
            updateDataPoints()
        }
    }
    
    private func startDataCollection() {
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            collectDataPoint()
        }
    }
    
    private func stopDataCollection() {
        timer?.invalidate()
        timer = nil
    }
    
    private func collectDataPoint() {
        let point = DataPoint(
            timestamp: Date(),
            bytesSent: Double(communicationManager.bytesSent),
            bytesReceived: Double(communicationManager.bytesReceived),
            latency: communicationManager.latency
        )
        
        dataPoints.append(point)
        
        // 保持数据点数量在合理范围内
        let maxPoints = selectedTimeRange.maxDataPoints
        if dataPoints.count > maxPoints {
            dataPoints.removeFirst(dataPoints.count - maxPoints)
        }
    }
    
    private func updateDataPoints() {
        // 根据时间范围过滤数据点
        let cutoffTime = Date().addingTimeInterval(-selectedTimeRange.timeInterval)
        dataPoints = dataPoints.filter { $0.timestamp >= cutoffTime }
    }
    
    private func getConnectionDuration() -> TimeInterval {
        // 这里应该从通信管理器获取连接开始时间
        // 暂时返回0
        return 0
    }
    
    private func formatBytes(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.semibold)
                .lineLimit(1)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(12)
    }
}

struct DataPoint: Identifiable {
    let id = UUID()
    let timestamp: Date
    let bytesSent: Double
    let bytesReceived: Double
    let latency: TimeInterval
}

struct SimpleChartView: View {
    let dataPoints: [DataPoint]
    let type: ChartType

    var body: some View {
        if dataPoints.isEmpty {
            Text("暂无数据")
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
        } else {
            GeometryReader { geometry in
                Path { path in
                    let width = geometry.size.width
                    let height = geometry.size.height
                    let stepX = width / CGFloat(max(dataPoints.count - 1, 1))

                    let values = dataPoints.map { point in
                        switch type {
                        case .dataTransfer:
                            return point.bytesSent
                        case .latency:
                            return point.latency * 1000
                        }
                    }

                    let maxValue = values.max() ?? 1
                    let minValue = values.min() ?? 0
                    let range = max(maxValue - minValue, 1)

                    for (index, value) in values.enumerated() {
                        let x = CGFloat(index) * stepX
                        let y = height - (CGFloat(value - minValue) / CGFloat(range)) * height

                        if index == 0 {
                            path.move(to: CGPoint(x: x, y: y))
                        } else {
                            path.addLine(to: CGPoint(x: x, y: y))
                        }
                    }
                }
                .stroke(type == .dataTransfer ? Color.blue : Color.orange, lineWidth: 2)
            }
        }
    }
}

enum ChartType {
    case dataTransfer
    case latency
}

enum TimeRange: CaseIterable {
    case minute
    case hour
    case day

    var displayName: String {
        switch self {
        case .minute:
            return "1分钟"
        case .hour:
            return "1小时"
        case .day:
            return "1天"
        }
    }

    var timeInterval: TimeInterval {
        switch self {
        case .minute:
            return 60
        case .hour:
            return 3600
        case .day:
            return 86400
        }
    }

    var maxDataPoints: Int {
        switch self {
        case .minute:
            return 60
        case .hour:
            return 360
        case .day:
            return 1440
        }
    }
}

#Preview {
    StatisticsView(
        communicationManager: CommunicationManager(
            usbManager: USBManager(),
            eventCaptureManager: EventCaptureManager()
        )
    )
}
