//
//  ContentView.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var usbManager = USBManager()
    @StateObject private var eventCaptureManager = EventCaptureManager()
    @StateObject private var communicationManager: CommunicationManager

    @State private var selectedTab = 0
    @State private var showingSettings = false

    init() {
        let usbMgr = USBManager()
        let eventMgr = EventCaptureManager()
        let commMgr = CommunicationManager(usbManager: usbMgr, eventCaptureManager: eventMgr)

        _usbManager = StateObject(wrappedValue: usbMgr)
        _eventCaptureManager = StateObject(wrappedValue: eventMgr)
        _communicationManager = StateObject(wrappedValue: commMgr)
    }

    var body: some View {
        NavigationView {
            TabView(selection: $selectedTab) {
                // 主界面
                MainView(
                    usbManager: usbManager,
                    eventCaptureManager: eventCaptureManager,
                    communicationManager: communicationManager
                )
                .tabItem {
                    Image(systemName: "computermouse")
                    Text("主界面")
                }
                .tag(0)

                // 设备管理
                DeviceView(usbManager: usbManager)
                .tabItem {
                    Image(systemName: "externaldrive.connected.to.line.below")
                    Text("设备")
                }
                .tag(1)

                // 统计信息
                StatisticsView(communicationManager: communicationManager)
                .tabItem {
                    Image(systemName: "chart.bar")
                    Text("统计")
                }
                .tag(2)

                // 设置
                SettingsView(eventCaptureManager: eventCaptureManager)
                .tabItem {
                    Image(systemName: "gear")
                    Text("设置")
                }
                .tag(3)
            }
            .navigationTitle("键鼠共享")
        }
        .frame(minWidth: 800, minHeight: 600)
    }
}

#Preview {
    ContentView()
}
