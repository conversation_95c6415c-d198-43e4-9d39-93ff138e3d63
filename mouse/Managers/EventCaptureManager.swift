//
//  EventCaptureManager.swift
//  mouse
//
//  Created by linco<PERSON> on 7/14/25.
//

import Foundation
import CoreGraphics
import ApplicationServices
import Combine

class EventCaptureManager: ObservableObject {
    @Published var isCapturing = false
    @Published var capturedEventsCount = 0
    
    private var eventTap: CFMachPort?
    private var runLoopSource: CFRunLoopSource?
    private var eventSubject = PassthroughSubject<MouseKeyboardEvent, Never>()
    
    // 事件发布者
    var eventPublisher: AnyPublisher<MouseKeyboardEvent, Never> {
        eventSubject.eraseToAnyPublisher()
    }
    
    // 热键设置
    private var hotkeyEnabled = true
    private var hotkeyModifiers: CGEventFlags = [.maskCommand, .maskShift]
    private var hotkeyKeyCode: CGKeyCode = 0x31 // Space key
    
    init() {
        requestAccessibilityPermissions()
    }
    
    deinit {
        stopCapturing()
    }
    
    // MARK: - Permissions
    private func requestAccessibilityPermissions() {
        let trusted = AXIsProcessTrusted()
        if !trusted {
            // 请求辅助功能权限
            let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue(): true] as CFDictionary
            AXIsProcessTrustedWithOptions(options)
        }
    }
    
    func checkPermissions() -> Bool {
        return AXIsProcessTrusted()
    }
    
    // MARK: - Event Capturing
    func startCapturing() {
        guard !isCapturing else { return }
        guard checkPermissions() else {
            print("Accessibility permissions not granted")
            return
        }
        
        let eventMask = (1 << CGEventType.mouseMoved.rawValue) |
                       (1 << CGEventType.leftMouseDown.rawValue) |
                       (1 << CGEventType.leftMouseUp.rawValue) |
                       (1 << CGEventType.rightMouseDown.rawValue) |
                       (1 << CGEventType.rightMouseUp.rawValue) |
                       (1 << CGEventType.otherMouseDown.rawValue) |
                       (1 << CGEventType.otherMouseUp.rawValue) |
                       (1 << CGEventType.scrollWheel.rawValue) |
                       (1 << CGEventType.keyDown.rawValue) |
                       (1 << CGEventType.keyUp.rawValue) |
                       (1 << CGEventType.flagsChanged.rawValue)
        
        let callback: CGEventTapCallBack = { (proxy, type, event, refcon) in
            let manager = Unmanaged<EventCaptureManager>.fromOpaque(refcon!).takeUnretainedValue()
            return manager.handleEvent(proxy: proxy, type: type, event: event)
        }
        
        eventTap = CGEvent.tapCreate(
            tap: .cgSessionEventTap,
            place: .headInsertEventTap,
            options: .defaultTap,
            eventsOfInterest: CGEventMask(eventMask),
            callback: callback,
            userInfo: Unmanaged.passUnretained(self).toOpaque()
        )
        
        guard let eventTap = eventTap else {
            print("Failed to create event tap")
            return
        }
        
        runLoopSource = CFMachPortCreateRunLoopSource(kCFAllocatorDefault, eventTap, 0)
        CFRunLoopAddSource(CFRunLoopGetCurrent(), runLoopSource, .commonModes)
        CGEvent.tapEnable(tap: eventTap, enable: true)
        
        isCapturing = true
        print("Event capturing started")
    }
    
    func stopCapturing() {
        guard isCapturing else { return }
        
        if let eventTap = eventTap {
            CGEvent.tapEnable(tap: eventTap, enable: false)
            CFMachPortInvalidate(eventTap)
            self.eventTap = nil
        }
        
        if let runLoopSource = runLoopSource {
            CFRunLoopRemoveSource(CFRunLoopGetCurrent(), runLoopSource, .commonModes)
            self.runLoopSource = nil
        }
        
        isCapturing = false
        print("Event capturing stopped")
    }
    
    private func handleEvent(proxy: CGEventTapProxy, type: CGEventType, event: CGEvent) -> Unmanaged<CGEvent>? {
        // 检查是否是热键
        if hotkeyEnabled && isHotkey(event: event, type: type) {
            let hotkeyEvent = MouseKeyboardEvent(
                type: .hotkey,
                keyboardData: KeyboardEventData(
                    keyCode: UInt16(event.getIntegerValueField(.keyboardEventKeycode)),
                    characters: nil,
                    modifierFlags: event.flags.rawValue,
                    isRepeat: false
                )
            )
            publishEvent(hotkeyEvent)
            return nil // 消费热键事件
        }
        
        // 处理其他事件
        if let mouseKeyboardEvent = convertToMouseKeyboardEvent(cgEvent: event, type: type) {
            publishEvent(mouseKeyboardEvent)
        }
        
        return Unmanaged.passUnretained(event)
    }
    
    private func isHotkey(event: CGEvent, type: CGEventType) -> Bool {
        guard type == .keyDown else { return false }
        
        let keyCode = CGKeyCode(event.getIntegerValueField(.keyboardEventKeycode))
        let flags = event.flags
        
        return keyCode == hotkeyKeyCode && flags.contains(hotkeyModifiers)
    }
    
    private func convertToMouseKeyboardEvent(cgEvent: CGEvent, type: CGEventType) -> MouseKeyboardEvent? {
        switch type {
        case .mouseMoved:
            let location = cgEvent.location
            return MouseKeyboardEvent(
                type: .mouseMove,
                mouseData: MouseEventData(x: location.x, y: location.y)
            )
            
        case .leftMouseDown:
            let location = cgEvent.location
            let clickCount = cgEvent.getIntegerValueField(.mouseEventClickState)
            return MouseKeyboardEvent(
                type: .mouseLeftDown,
                mouseData: MouseEventData(x: location.x, y: location.y, clickCount: Int(clickCount))
            )
            
        case .leftMouseUp:
            let location = cgEvent.location
            return MouseKeyboardEvent(
                type: .mouseLeftUp,
                mouseData: MouseEventData(x: location.x, y: location.y)
            )
            
        case .rightMouseDown:
            let location = cgEvent.location
            return MouseKeyboardEvent(
                type: .mouseRightDown,
                mouseData: MouseEventData(x: location.x, y: location.y)
            )
            
        case .rightMouseUp:
            let location = cgEvent.location
            return MouseKeyboardEvent(
                type: .mouseRightUp,
                mouseData: MouseEventData(x: location.x, y: location.y)
            )
            
        case .otherMouseDown:
            let location = cgEvent.location
            return MouseKeyboardEvent(
                type: .mouseMiddleDown,
                mouseData: MouseEventData(x: location.x, y: location.y)
            )
            
        case .otherMouseUp:
            let location = cgEvent.location
            return MouseKeyboardEvent(
                type: .mouseMiddleUp,
                mouseData: MouseEventData(x: location.x, y: location.y)
            )
            
        case .scrollWheel:
            let location = cgEvent.location
            let scrollDeltaX = Double(cgEvent.getIntegerValueField(.scrollWheelEventDeltaAxis2))
            let scrollDeltaY = Double(cgEvent.getIntegerValueField(.scrollWheelEventDeltaAxis1))
            return MouseKeyboardEvent(
                type: .mouseScroll,
                mouseData: MouseEventData(
                    x: location.x,
                    y: location.y,
                    scrollDeltaX: scrollDeltaX,
                    scrollDeltaY: scrollDeltaY
                )
            )
            
        case .keyDown:
            let keyCode = UInt16(cgEvent.getIntegerValueField(.keyboardEventKeycode))
            let characters = cgEvent.keyboardGetUnicodeString()
            let modifierFlags = cgEvent.flags.rawValue
            let isRepeat = cgEvent.getIntegerValueField(.keyboardEventAutorepeat) != 0
            
            return MouseKeyboardEvent(
                type: .keyDown,
                keyboardData: KeyboardEventData(
                    keyCode: keyCode,
                    characters: characters,
                    modifierFlags: modifierFlags,
                    isRepeat: isRepeat
                )
            )
            
        case .keyUp:
            let keyCode = UInt16(cgEvent.getIntegerValueField(.keyboardEventKeycode))
            let characters = cgEvent.keyboardGetUnicodeString()
            let modifierFlags = cgEvent.flags.rawValue
            
            return MouseKeyboardEvent(
                type: .keyUp,
                keyboardData: KeyboardEventData(
                    keyCode: keyCode,
                    characters: characters,
                    modifierFlags: modifierFlags,
                    isRepeat: false
                )
            )
            
        case .flagsChanged:
            let modifierFlags = cgEvent.flags.rawValue
            return MouseKeyboardEvent(
                type: .modifierChanged,
                keyboardData: KeyboardEventData(
                    keyCode: 0,
                    characters: nil,
                    modifierFlags: modifierFlags,
                    isRepeat: false
                )
            )
            
        default:
            return nil
        }
    }
    
    private func publishEvent(_ event: MouseKeyboardEvent) {
        DispatchQueue.main.async {
            self.capturedEventsCount += 1
            self.eventSubject.send(event)
        }
    }
    
    // MARK: - Hotkey Configuration
    func setHotkey(modifiers: CGEventFlags, keyCode: CGKeyCode) {
        hotkeyModifiers = modifiers
        hotkeyKeyCode = keyCode
    }
    
    func enableHotkey(_ enabled: Bool) {
        hotkeyEnabled = enabled
    }
}

// MARK: - CGEvent Extensions
extension CGEvent {
    func keyboardGetUnicodeString() -> String? {
        let maxLength = 4
        var actualLength = 0
        var unicodeString = [UniChar](repeating: 0, count: maxLength)
        
        keyboardGetUnicodeString(length: maxLength, actualLength: &actualLength, unicodeString: &unicodeString)
        
        if actualLength > 0 {
            return String(utf16CodeUnits: unicodeString, count: actualLength)
        }
        return nil
    }
}
