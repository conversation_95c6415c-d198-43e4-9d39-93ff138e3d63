//
//  ConfigurationManager.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import CoreGraphics
import Combine
import AppKit

class ConfigurationManager: ObservableObject {
    static let shared = ConfigurationManager()
    
    // 应用设置
    @Published var autoStartCapture: Bool {
        didSet { UserDefaults.standard.set(autoStartCapture, forKey: "autoStartCapture") }
    }
    
    @Published var showNotifications: Bool {
        didSet { UserDefaults.standard.set(showNotifications, forKey: "showNotifications") }
    }
    
    @Published var minimizeToTray: Bool {
        didSet { UserDefaults.standard.set(minimizeToTray, forKey: "minimizeToTray") }
    }
    
    // 热键设置
    @Published var hotkeyEnabled: Bool {
        didSet { UserDefaults.standard.set(hotkeyEnabled, forKey: "hotkeyEnabled") }
    }
    
    @Published var hotkeyModifiers: CGEventFlags {
        didSet { 
            UserDefaults.standard.set(hotkeyModifiers.rawValue, forKey: "hotkeyModifiers")
        }
    }
    
    @Published var hotkeyKeyCode: CGKeyCode {
        didSet { UserDefaults.standard.set(Int(hotkeyKeyCode), forKey: "hotkeyKeyCode") }
    }
    
    // 连接设置
    @Published var bufferSize: Int {
        didSet { UserDefaults.standard.set(bufferSize, forKey: "bufferSize") }
    }
    
    @Published var reconnectAttempts: Int {
        didSet { UserDefaults.standard.set(reconnectAttempts, forKey: "reconnectAttempts") }
    }
    
    @Published var heartbeatInterval: TimeInterval {
        didSet { UserDefaults.standard.set(heartbeatInterval, forKey: "heartbeatInterval") }
    }
    
    // 设备设置
    @Published var lastSelectedDeviceID: String? {
        didSet { UserDefaults.standard.set(lastSelectedDeviceID, forKey: "lastSelectedDeviceID") }
    }
    
    @Published var autoConnectToLastDevice: Bool {
        didSet { UserDefaults.standard.set(autoConnectToLastDevice, forKey: "autoConnectToLastDevice") }
    }
    
    private init() {
        // 加载保存的设置
        self.autoStartCapture = UserDefaults.standard.bool(forKey: "autoStartCapture")
        self.showNotifications = UserDefaults.standard.object(forKey: "showNotifications") as? Bool ?? true
        self.minimizeToTray = UserDefaults.standard.bool(forKey: "minimizeToTray")
        
        self.hotkeyEnabled = UserDefaults.standard.object(forKey: "hotkeyEnabled") as? Bool ?? true
        self.hotkeyModifiers = CGEventFlags(rawValue: UserDefaults.standard.object(forKey: "hotkeyModifiers") as? UInt64 ?? (CGEventFlags.maskCommand.rawValue | CGEventFlags.maskShift.rawValue))
        self.hotkeyKeyCode = CGKeyCode(UserDefaults.standard.object(forKey: "hotkeyKeyCode") as? Int ?? 0x31)
        
        self.bufferSize = UserDefaults.standard.object(forKey: "bufferSize") as? Int ?? 1024
        self.reconnectAttempts = UserDefaults.standard.object(forKey: "reconnectAttempts") as? Int ?? 5
        self.heartbeatInterval = UserDefaults.standard.object(forKey: "heartbeatInterval") as? TimeInterval ?? 5.0
        
        self.lastSelectedDeviceID = UserDefaults.standard.string(forKey: "lastSelectedDeviceID")
        self.autoConnectToLastDevice = UserDefaults.standard.object(forKey: "autoConnectToLastDevice") as? Bool ?? true
    }
    
    // MARK: - 配置导出/导入
    func exportConfiguration() -> [String: Any] {
        return [
            "autoStartCapture": autoStartCapture,
            "showNotifications": showNotifications,
            "minimizeToTray": minimizeToTray,
            "hotkeyEnabled": hotkeyEnabled,
            "hotkeyModifiers": hotkeyModifiers.rawValue,
            "hotkeyKeyCode": Int(hotkeyKeyCode),
            "bufferSize": bufferSize,
            "reconnectAttempts": reconnectAttempts,
            "heartbeatInterval": heartbeatInterval,
            "autoConnectToLastDevice": autoConnectToLastDevice
        ]
    }
    
    func importConfiguration(_ config: [String: Any]) {
        if let value = config["autoStartCapture"] as? Bool {
            autoStartCapture = value
        }
        if let value = config["showNotifications"] as? Bool {
            showNotifications = value
        }
        if let value = config["minimizeToTray"] as? Bool {
            minimizeToTray = value
        }
        if let value = config["hotkeyEnabled"] as? Bool {
            hotkeyEnabled = value
        }
        if let value = config["hotkeyModifiers"] as? UInt64 {
            hotkeyModifiers = CGEventFlags(rawValue: value)
        }
        if let value = config["hotkeyKeyCode"] as? Int {
            hotkeyKeyCode = CGKeyCode(value)
        }
        if let value = config["bufferSize"] as? Int {
            bufferSize = value
        }
        if let value = config["reconnectAttempts"] as? Int {
            reconnectAttempts = value
        }
        if let value = config["heartbeatInterval"] as? TimeInterval {
            heartbeatInterval = value
        }
        if let value = config["autoConnectToLastDevice"] as? Bool {
            autoConnectToLastDevice = value
        }
    }
    
    func resetToDefaults() {
        autoStartCapture = false
        showNotifications = true
        minimizeToTray = false
        hotkeyEnabled = true
        hotkeyModifiers = [.maskCommand, .maskShift]
        hotkeyKeyCode = 0x31
        bufferSize = 1024
        reconnectAttempts = 5
        heartbeatInterval = 5.0
        lastSelectedDeviceID = nil
        autoConnectToLastDevice = true
    }
    
    // MARK: - 热键描述
    func hotkeyDescription() -> String {
        var parts: [String] = []
        
        if hotkeyModifiers.contains(.maskCommand) {
            parts.append("⌘")
        }
        if hotkeyModifiers.contains(.maskShift) {
            parts.append("⇧")
        }
        if hotkeyModifiers.contains(.maskAlternate) {
            parts.append("⌥")
        }
        if hotkeyModifiers.contains(.maskControl) {
            parts.append("⌃")
        }
        
        parts.append(keyCodeToString(hotkeyKeyCode))
        
        return parts.joined(separator: " + ")
    }
    
    private func keyCodeToString(_ keyCode: CGKeyCode) -> String {
        switch keyCode {
        case 0x31: return "Space"
        case 0x35: return "Esc"
        case 0x24: return "Return"
        case 0x30: return "Tab"
        case 0x7E: return "↑"
        case 0x7D: return "↓"
        case 0x7B: return "←"
        case 0x7C: return "→"
        case 0x00: return "A"
        case 0x0B: return "B"
        case 0x08: return "C"
        case 0x02: return "D"
        case 0x0E: return "E"
        case 0x03: return "F"
        case 0x05: return "G"
        case 0x04: return "H"
        case 0x22: return "I"
        case 0x26: return "J"
        case 0x28: return "K"
        case 0x25: return "L"
        case 0x2E: return "M"
        case 0x2D: return "N"
        case 0x1F: return "O"
        case 0x23: return "P"
        case 0x0C: return "Q"
        case 0x0F: return "R"
        case 0x01: return "S"
        case 0x11: return "T"
        case 0x20: return "U"
        case 0x09: return "V"
        case 0x0D: return "W"
        case 0x07: return "X"
        case 0x10: return "Y"
        case 0x06: return "Z"
        case 0x1D: return "0"
        case 0x12: return "1"
        case 0x13: return "2"
        case 0x14: return "3"
        case 0x15: return "4"
        case 0x17: return "5"
        case 0x16: return "6"
        case 0x1A: return "7"
        case 0x1C: return "8"
        case 0x19: return "9"
        case 0x7A: return "F1"
        case 0x78: return "F2"
        case 0x63: return "F3"
        case 0x76: return "F4"
        case 0x60: return "F5"
        case 0x61: return "F6"
        case 0x62: return "F7"
        case 0x64: return "F8"
        case 0x65: return "F9"
        case 0x6D: return "F10"
        case 0x67: return "F11"
        case 0x6F: return "F12"
        default: return "Key \(keyCode)"
        }
    }
}

// MARK: - 通知扩展
extension ConfigurationManager {
    func postNotification(title: String, message: String) {
        guard showNotifications else { return }
        
        let notification = NSUserNotification()
        notification.title = title
        notification.informativeText = message
        notification.soundName = NSUserNotificationDefaultSoundName
        
        NSUserNotificationCenter.default.deliver(notification)
    }
}

// MARK: - 配置文件管理
extension ConfigurationManager {
    func saveConfigurationToFile(url: URL) throws {
        let config = exportConfiguration()
        let data = try JSONSerialization.data(withJSONObject: config, options: .prettyPrinted)
        try data.write(to: url)
    }
    
    func loadConfigurationFromFile(url: URL) throws {
        let data = try Data(contentsOf: url)
        let config = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
        
        if let config = config {
            importConfiguration(config)
        }
    }
}
