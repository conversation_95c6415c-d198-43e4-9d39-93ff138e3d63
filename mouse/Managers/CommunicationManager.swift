//
//  CommunicationManager.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import Combine

class CommunicationManager: ObservableObject {
    @Published var isConnected = false
    @Published var bytesSent: UInt64 = 0
    @Published var bytesReceived: UInt64 = 0
    @Published var latency: TimeInterval = 0
    
    private let usbManager: USBManager
    private let eventCaptureManager: EventCaptureManager
    private var cancellables = Set<AnyCancellable>()
    
    // 数据缓冲区
    private var sendBuffer = Data()
    private var receiveBuffer = Data()
    private let bufferQueue = DispatchQueue(label: "communication.buffer", qos: .userInitiated)
    
    // 心跳机制
    private var heartbeatTimer: Timer?
    private let heartbeatInterval: TimeInterval = 5.0
    private var lastHeartbeatTime: Date?
    
    // 重连机制
    private var reconnectTimer: Timer?
    private let maxReconnectAttempts = 5
    private var reconnectAttempts = 0
    
    init(usbManager: USBManager, eventCaptureManager: EventCaptureManager) {
        self.usbManager = usbManager
        self.eventCaptureManager = eventCaptureManager
        
        setupBindings()
    }
    
    deinit {
        disconnect()
    }
    
    // MARK: - Setup
    private func setupBindings() {
        // 监听USB连接状态
        usbManager.$connectionState
            .sink { [weak self] state in
                self?.handleConnectionStateChange(state)
            }
            .store(in: &cancellables)
        
        // 监听捕获的事件
        eventCaptureManager.eventPublisher
            .sink { [weak self] event in
                self?.handleCapturedEvent(event)
            }
            .store(in: &cancellables)
    }
    
    private func handleConnectionStateChange(_ state: ConnectionState) {
        DispatchQueue.main.async {
            switch state {
            case .connected:
                self.isConnected = true
                self.startHeartbeat()
                self.sendHandshake()
                self.reconnectAttempts = 0
                
            case .disconnected:
                self.isConnected = false
                self.stopHeartbeat()
                
            case .error(let message):
                self.isConnected = false
                self.stopHeartbeat()
                print("Connection error: \(message)")
                self.scheduleReconnect()
                
            case .connecting:
                break
            }
        }
    }
    
    // MARK: - Event Handling
    private func handleCapturedEvent(_ event: MouseKeyboardEvent) {
        guard isConnected else { return }
        
        do {
            let packet = try CommunicationPacket(event: event)
            sendPacket(packet)
        } catch {
            print("Failed to create packet: \(error)")
        }
    }
    
    // MARK: - Packet Transmission
    private func sendPacket(_ packet: CommunicationPacket) {
        bufferQueue.async {
            do {
                let encoder = JSONEncoder()
                let data = try encoder.encode(packet)
                
                // 添加包长度前缀
                var lengthData = Data()
                let length = UInt32(data.count)
                lengthData.append(contentsOf: withUnsafeBytes(of: length.bigEndian) { Array($0) })
                
                let fullData = lengthData + data
                self.usbManager.sendData(fullData)
                
                DispatchQueue.main.async {
                    self.bytesSent += UInt64(fullData.count)
                }
            } catch {
                print("Failed to encode packet: \(error)")
            }
        }
    }
    
    private func sendHandshake() {
        let deviceInfo = DeviceInfo()
        let handshakePacket = HandshakePacket(deviceInfo: deviceInfo)
        
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(handshakePacket)
            usbManager.sendData(data)
            print("Handshake sent")
        } catch {
            print("Failed to send handshake: \(error)")
        }
    }
    
    // MARK: - Heartbeat
    private func startHeartbeat() {
        stopHeartbeat()
        
        heartbeatTimer = Timer.scheduledTimer(withTimeInterval: heartbeatInterval, repeats: true) { [weak self] _ in
            self?.sendHeartbeat()
        }
    }
    
    private func stopHeartbeat() {
        heartbeatTimer?.invalidate()
        heartbeatTimer = nil
    }
    
    private func sendHeartbeat() {
        let heartbeat = HeartbeatPacket()
        
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(heartbeat)
            usbManager.sendData(data)
            lastHeartbeatTime = Date()
        } catch {
            print("Failed to send heartbeat: \(error)")
        }
    }
    
    // MARK: - Reconnection
    private func scheduleReconnect() {
        guard reconnectAttempts < maxReconnectAttempts else {
            print("Max reconnect attempts reached")
            return
        }
        
        reconnectAttempts += 1
        let delay = TimeInterval(reconnectAttempts * 2) // 指数退避
        
        reconnectTimer?.invalidate()
        reconnectTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            self?.attemptReconnect()
        }
    }
    
    private func attemptReconnect() {
        guard let selectedDevice = usbManager.selectedDevice else { return }
        
        print("Attempting reconnect (\(reconnectAttempts)/\(maxReconnectAttempts))")
        usbManager.connectToDevice(selectedDevice)
    }
    
    // MARK: - Public Methods
    func connect() {
        guard let selectedDevice = usbManager.selectedDevice else {
            print("No device selected")
            return
        }
        
        usbManager.connectToDevice(selectedDevice)
    }
    
    func disconnect() {
        stopHeartbeat()
        reconnectTimer?.invalidate()
        reconnectTimer = nil
        usbManager.disconnect()
    }
    
    func getConnectionInfo() -> ConnectionInfo {
        return ConnectionInfo(
            isConnected: isConnected,
            deviceName: usbManager.selectedDevice?.name ?? "None",
            bytesSent: bytesSent,
            bytesReceived: bytesReceived,
            latency: latency,
            lastHeartbeat: lastHeartbeatTime
        )
    }
}

// MARK: - Supporting Structures
struct HandshakePacket: Codable {
    let type: String = "handshake"
    let deviceInfo: DeviceInfo
    let timestamp: TimeInterval
    
    init(deviceInfo: DeviceInfo) {
        self.deviceInfo = deviceInfo
        self.timestamp = Date().timeIntervalSince1970
    }
}

struct HeartbeatPacket: Codable {
    let type: String = "heartbeat"
    let timestamp: TimeInterval
    
    init() {
        self.timestamp = Date().timeIntervalSince1970
    }
}

struct ConnectionInfo {
    let isConnected: Bool
    let deviceName: String
    let bytesSent: UInt64
    let bytesReceived: UInt64
    let latency: TimeInterval
    let lastHeartbeat: Date?
}

// MARK: - Data Processing Extensions
extension CommunicationManager {
    func processReceivedData(_ data: Data) {
        bufferQueue.async {
            self.receiveBuffer.append(data)
            self.processReceiveBuffer()
            
            DispatchQueue.main.async {
                self.bytesReceived += UInt64(data.count)
            }
        }
    }
    
    private func processReceiveBuffer() {
        while receiveBuffer.count >= 4 {
            // 读取包长度
            let lengthData = receiveBuffer.prefix(4)
            let length = lengthData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
            
            guard receiveBuffer.count >= 4 + length else {
                break // 数据不完整，等待更多数据
            }
            
            // 提取完整包
            let packetData = receiveBuffer.subdata(in: 4..<Int(4 + length))
            receiveBuffer.removeFirst(Int(4 + length))
            
            // 处理包
            processPacket(packetData)
        }
    }
    
    private func processPacket(_ data: Data) {
        do {
            let decoder = JSONDecoder()
            
            // 尝试解析不同类型的包
            if let handshake = try? decoder.decode(HandshakePacket.self, from: data) {
                handleHandshakePacket(handshake)
            } else if let heartbeat = try? decoder.decode(HeartbeatPacket.self, from: data) {
                handleHeartbeatPacket(heartbeat)
            } else if let packet = try? decoder.decode(CommunicationPacket.self, from: data) {
                handleCommunicationPacket(packet)
            } else {
                print("Unknown packet type received")
            }
        } catch {
            print("Failed to process packet: \(error)")
        }
    }
    
    private func handleHandshakePacket(_ packet: HandshakePacket) {
        print("Received handshake from: \(packet.deviceInfo.deviceName)")
        // 可以在这里验证设备信息
    }
    
    private func handleHeartbeatPacket(_ packet: HeartbeatPacket) {
        let now = Date().timeIntervalSince1970
        let latency = now - packet.timestamp
        
        DispatchQueue.main.async {
            self.latency = latency
        }
    }
    
    private func handleCommunicationPacket(_ packet: CommunicationPacket) {
        // 验证校验和
        let calculatedChecksum = CommunicationPacket.calculateChecksum(data: packet.payload)
        guard calculatedChecksum == packet.checksum else {
            print("Packet checksum mismatch")
            return
        }
        
        // 解析事件数据
        do {
            let decoder = JSONDecoder()
            let event = try decoder.decode(MouseKeyboardEvent.self, from: packet.payload)
            handleRemoteEvent(event)
        } catch {
            print("Failed to decode event: \(error)")
        }
    }
    
    private func handleRemoteEvent(_ event: MouseKeyboardEvent) {
        // 处理来自远程设备的事件
        // 这里可以实现事件的本地回放
        print("Received remote event: \(event.type)")
    }
}
