//
//  USBManager.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import Foundation
import IOKit
import IOKit.usb
import IOKit.serial
import Combine

class USBManager: ObservableObject {
    @Published var connectionState: ConnectionState = .disconnected
    @Published var connectedDevices: [USBDevice] = []
    @Published var selectedDevice: USBDevice?
    
    private var notificationPort: IONotificationPortRef?
    private var addedIterator: io_iterator_t = 0
    private var removedIterator: io_iterator_t = 0
    private var serialPort: FileHandle?
    private var readSource: DispatchSourceRead?
    
    // Communication settings
    private let vendorID: UInt16 = 0x1234  // 自定义厂商ID
    private let productID: UInt16 = 0x5678  // 自定义产品ID
    private let baudRate: speed_t = speed_t(B115200)
    
    init() {
        setupUSBNotifications()
        scanForDevices()
    }
    
    deinit {
        cleanup()
    }
    
    // MARK: - USB Device Detection
    private func setupUSBNotifications() {
        notificationPort = IONotificationPortCreate(kIOMainPortDefault)
        guard let notificationPort = notificationPort else {
            print("Failed to create notification port")
            return
        }
        
        let runLoopSource = IONotificationPortGetRunLoopSource(notificationPort)
        CFRunLoopAddSource(CFRunLoopGetCurrent(), runLoopSource!.takeUnretainedValue(), CFRunLoopMode.defaultMode)
        
        // 监听USB设备插入
        let matchingDict = IOServiceMatching(kIOUSBDeviceClassName)
        let addedCallback: IOServiceMatchingCallback = { (refcon, iterator) in
            let manager = Unmanaged<USBManager>.fromOpaque(refcon!).takeUnretainedValue()
            manager.deviceAdded(iterator: iterator)
        }
        
        IOServiceAddMatchingNotification(
            notificationPort,
            kIOFirstMatchNotification,
            matchingDict,
            addedCallback,
            Unmanaged.passUnretained(self).toOpaque(),
            &addedIterator
        )
        
        // 监听USB设备移除
        let removedCallback: IOServiceMatchingCallback = { (refcon, iterator) in
            let manager = Unmanaged<USBManager>.fromOpaque(refcon!).takeUnretainedValue()
            manager.deviceRemoved(iterator: iterator)
        }
        
        IOServiceAddMatchingNotification(
            notificationPort,
            kIOTerminatedNotification,
            matchingDict,
            removedCallback,
            Unmanaged.passUnretained(self).toOpaque(),
            &removedIterator
        )
        
        // 清空初始迭代器
        deviceAdded(iterator: addedIterator)
        deviceRemoved(iterator: removedIterator)
    }
    
    private func deviceAdded(iterator: io_iterator_t) {
        var service: io_service_t = 0
        while IOIteratorNext(iterator) != 0 {
            service = IOIteratorNext(iterator)
            if service != 0 {
                if let device = createUSBDevice(from: service) {
                    DispatchQueue.main.async {
                        self.connectedDevices.append(device)
                        if self.selectedDevice == nil && device.isMouseKeyboardDevice {
                            self.selectedDevice = device
                        }
                    }
                }
                IOObjectRelease(service)
            }
        }
    }
    
    private func deviceRemoved(iterator: io_iterator_t) {
        var service: io_service_t = 0
        while IOIteratorNext(iterator) != 0 {
            service = IOIteratorNext(iterator)
            if service != 0 {
                // 处理设备移除
                DispatchQueue.main.async {
                    // 这里可以根据service信息移除对应设备
                    self.handleDeviceDisconnection()
                }
                IOObjectRelease(service)
            }
        }
    }
    
    private func createUSBDevice(from service: io_service_t) -> USBDevice? {
        var vendorID: UInt16 = 0
        var productID: UInt16 = 0
        var deviceName = "Unknown Device"
        
        // 获取厂商ID
        if let vendorIDRef = IORegistryEntryCreateCFProperty(service, kUSBVendorID as CFString, kCFAllocatorDefault, 0) {
            if CFGetTypeID(vendorIDRef.takeUnretainedValue()) == CFNumberGetTypeID() {
                CFNumberGetValue((vendorIDRef.takeUnretainedValue() as! CFNumber), .sInt16Type, &vendorID)
            }
            vendorIDRef.release()
        }

        // 获取产品ID
        if let productIDRef = IORegistryEntryCreateCFProperty(service, kUSBProductID as CFString, kCFAllocatorDefault, 0) {
            if CFGetTypeID(productIDRef.takeUnretainedValue()) == CFNumberGetTypeID() {
                CFNumberGetValue((productIDRef.takeUnretainedValue() as! CFNumber), .sInt16Type, &productID)
            }
            productIDRef.release()
        }
        
        // 获取设备名称
        if let nameRef = IORegistryEntryCreateCFProperty(service, kUSBProductString as CFString, kCFAllocatorDefault, 0) {
            if CFGetTypeID(nameRef.takeUnretainedValue()) == CFStringGetTypeID() {
                deviceName = nameRef.takeUnretainedValue() as! String
            }
            nameRef.release()
        }
        
        return USBDevice(
            vendorID: vendorID,
            productID: productID,
            name: deviceName,
            service: service
        )
    }
    
    private func scanForDevices() {
        connectedDevices.removeAll()
        
        let matchingDict = IOServiceMatching(kIOUSBDeviceClassName)
        var iterator: io_iterator_t = 0
        
        let result = IOServiceGetMatchingServices(kIOMainPortDefault, matchingDict, &iterator)
        guard result == KERN_SUCCESS else {
            print("Failed to get USB devices")
            return
        }
        
        var service: io_service_t = 0
        while IOIteratorNext(iterator) != 0 {
            service = IOIteratorNext(iterator)
            if service != 0 {
                if let device = createUSBDevice(from: service) {
                    connectedDevices.append(device)
                }
                IOObjectRelease(service)
            }
        }
        
        IOObjectRelease(iterator)
    }
    
    // MARK: - Connection Management
    func connectToDevice(_ device: USBDevice) {
        guard connectionState != .connected else { return }
        
        connectionState = .connecting
        selectedDevice = device
        
        // 尝试建立串口连接
        DispatchQueue.global(qos: .userInitiated).async {
            self.establishSerialConnection(device)
        }
    }
    
    private func establishSerialConnection(_ device: USBDevice) {
        // 查找对应的串口设备
        guard let serialPath = findSerialPath(for: device) else {
            DispatchQueue.main.async {
                self.connectionState = .error("Serial port not found")
            }
            return
        }
        
        // 打开串口
        guard let fileHandle = FileHandle(forUpdatingAtPath: serialPath) else {
            DispatchQueue.main.async {
                self.connectionState = .error("Failed to open serial port")
            }
            return
        }
        
        serialPort = fileHandle
        setupSerialCommunication()
        
        DispatchQueue.main.async {
            self.connectionState = .connected
        }
    }
    
    private func findSerialPath(for device: USBDevice) -> String? {
        // 这里应该根据设备信息查找对应的串口路径
        // 通常在 /dev/tty.usbserial-* 或 /dev/cu.usbserial-*
        let fileManager = FileManager.default
        let devPath = "/dev"
        
        do {
            let files = try fileManager.contentsOfDirectory(atPath: devPath)
            for file in files {
                if file.hasPrefix("cu.usbserial") || file.hasPrefix("tty.usbserial") {
                    return "\(devPath)/\(file)"
                }
            }
        } catch {
            print("Error scanning /dev directory: \(error)")
        }
        
        return nil
    }
    
    private func setupSerialCommunication() {
        guard let serialPort = serialPort else { return }
        
        // 设置串口参数
        let fd = serialPort.fileDescriptor
        var options = termios()
        tcgetattr(fd, &options)
        
        // 设置波特率
        cfsetispeed(&options, baudRate)
        cfsetospeed(&options, baudRate)
        
        // 设置数据位、停止位、校验位
        options.c_cflag |= tcflag_t(CS8 | CREAD | CLOCAL)
        options.c_cflag &= ~tcflag_t(PARENB | CSTOPB | CSIZE)
        
        tcsetattr(fd, TCSANOW, &options)
        
        // 设置读取监听
        readSource = DispatchSource.makeReadSource(fileDescriptor: fd, queue: .global(qos: .userInitiated))
        readSource?.setEventHandler { [weak self] in
            self?.handleIncomingData()
        }
        readSource?.resume()
    }
    
    private func handleIncomingData() {
        guard let serialPort = serialPort else { return }
        
        let data = serialPort.availableData
        if !data.isEmpty {
            processReceivedData(data)
        }
    }
    
    private func processReceivedData(_ data: Data) {
        // 处理接收到的数据
        // 这里应该解析通信协议，处理来自Windows端的消息
        print("Received data: \(data.count) bytes")
    }
    
    func sendData(_ data: Data) {
        guard let serialPort = serialPort, connectionState == .connected else {
            print("Cannot send data: not connected")
            return
        }
        
        serialPort.write(data)
    }
    
    func disconnect() {
        cleanup()
        DispatchQueue.main.async {
            self.connectionState = .disconnected
            self.selectedDevice = nil
        }
    }
    
    private func handleDeviceDisconnection() {
        if connectionState == .connected {
            connectionState = .disconnected
            cleanup()
        }
    }
    
    private func cleanup() {
        readSource?.cancel()
        readSource = nil
        
        serialPort?.closeFile()
        serialPort = nil
        
        if notificationPort != nil {
            IONotificationPortDestroy(notificationPort)
            notificationPort = nil
        }
        
        if addedIterator != 0 {
            IOObjectRelease(addedIterator)
            addedIterator = 0
        }
        
        if removedIterator != 0 {
            IOObjectRelease(removedIterator)
            removedIterator = 0
        }
    }
}

// MARK: - USB Device Model
struct USBDevice: Identifiable, Equatable {
    let id = UUID()
    let vendorID: UInt16
    let productID: UInt16
    let name: String
    let service: io_service_t
    
    var isMouseKeyboardDevice: Bool {
        // 检查是否是键鼠共享设备
        return vendorID == 0x1234 && productID == 0x5678
    }
    
    static func == (lhs: USBDevice, rhs: USBDevice) -> Bool {
        return lhs.vendorID == rhs.vendorID && 
               lhs.productID == rhs.productID && 
               lhs.service == rhs.service
    }
}
