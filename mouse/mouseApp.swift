//
//  mouseApp.swift
//  mouse
//
//  Created by lincoo on 7/14/25.
//

import SwiftUI
import AppKit
import ApplicationServices

@main
struct mouseApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var appState = AppState()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appState)
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .commands {
            CommandGroup(replacing: .appInfo) {
                Button("关于键鼠共享") {
                    appState.showAbout = true
                }
            }

            CommandGroup(after: .appSettings) {
                Button("偏好设置...") {
                    appState.showSettings = true
                }
                .keyboardShortcut(",", modifiers: .command)
            }
        }

        Settings {
            SettingsView(eventCaptureManager: EventCaptureManager())
                .frame(width: 600, height: 500)
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    var statusItem: NSStatusItem?
    var appState: AppState?

    func applicationDidFinishLaunching(_ notification: Notification) {
        setupStatusItem()

        // 请求必要权限
        requestPermissions()
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // 如果启用了系统托盘模式，不要在关闭窗口时退出
        return !UserDefaults.standard.bool(forKey: "minimizeToTray")
    }

    private func setupStatusItem() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)

        if let button = statusItem?.button {
            button.image = NSImage(systemSymbolName: "computermouse", accessibilityDescription: "键鼠共享")
            button.action = #selector(statusItemClicked)
            button.target = self
        }

        setupStatusMenu()
    }

    private func setupStatusMenu() {
        let menu = NSMenu()

        menu.addItem(NSMenuItem(title: "显示主窗口", action: #selector(showMainWindow), keyEquivalent: ""))
        menu.addItem(NSMenuItem.separator())
        menu.addItem(NSMenuItem(title: "开始捕获", action: #selector(startCapture), keyEquivalent: ""))
        menu.addItem(NSMenuItem(title: "停止捕获", action: #selector(stopCapture), keyEquivalent: ""))
        menu.addItem(NSMenuItem.separator())
        menu.addItem(NSMenuItem(title: "偏好设置...", action: #selector(showSettings), keyEquivalent: ","))
        menu.addItem(NSMenuItem.separator())
        menu.addItem(NSMenuItem(title: "退出", action: #selector(quitApp), keyEquivalent: "q"))

        statusItem?.menu = menu
    }

    @objc private func statusItemClicked() {
        showMainWindow()
    }

    @objc private func showMainWindow() {
        NSApp.activate(ignoringOtherApps: true)
        for window in NSApp.windows {
            window.makeKeyAndOrderFront(nil)
        }
    }

    @objc private func startCapture() {
        // 通过通知或其他方式通知应用开始捕获
        NotificationCenter.default.post(name: .startCapture, object: nil)
    }

    @objc private func stopCapture() {
        // 通过通知或其他方式通知应用停止捕获
        NotificationCenter.default.post(name: .stopCapture, object: nil)
    }

    @objc private func showSettings() {
        NSApp.sendAction(Selector(("showSettingsWindow:")), to: nil, from: nil)
    }

    @objc private func quitApp() {
        NSApp.terminate(nil)
    }

    private func requestPermissions() {
        // 请求辅助功能权限
        let trusted = AXIsProcessTrusted()
        if !trusted {
            let alert = NSAlert()
            alert.messageText = "需要辅助功能权限"
            alert.informativeText = "此应用需要辅助功能权限来捕获键盘和鼠标事件。请在系统偏好设置中启用。"
            alert.addButton(withTitle: "打开系统偏好设置")
            alert.addButton(withTitle: "稍后")

            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
                NSWorkspace.shared.open(url)
            }
        }
    }
}

class AppState: ObservableObject {
    @Published var showAbout = false
    @Published var showSettings = false
}

extension Notification.Name {
    static let startCapture = Notification.Name("startCapture")
    static let stopCapture = Notification.Name("stopCapture")
}
